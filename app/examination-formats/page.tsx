'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { fetchSchools } from '@/actions/school.action';
import { getExaminationFormatAction } from '@/actions/examinationFormat.action';
import { ExaminationFormatUploader } from '@/components/organisms/ExaminationFormatManager/ExaminationFormatUploader';
import { Button } from '@/components/atoms/Button';
import { Spinner } from '@/components/atoms/Spinner';
import { Alert } from '@/components/atoms/Alert';
import { Input } from '@/components/atoms/Input';
import { FiSearch, FiEye, FiUpload, FiRefreshCw, FiCheck, FiX, FiClock } from 'react-icons/fi';
import { useSession } from 'next-auth/react';
import { EUserRole } from '@/types/user';

interface ISchool {
  id: string;
  name: string;
  address: string;
  phoneNumber: string;
  email: string;
}

interface FormatStatus {
  status: 'available' | 'unavailable' | 'processing' | 'error';
  error?: string;
}

const ExaminationFormatsPage = () => {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  
  const [schools, setSchools] = useState<ISchool[]>([]);
  const [filteredSchools, setFilteredSchools] = useState<ISchool[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formatStatuses, setFormatStatuses] = useState<Record<string, FormatStatus>>({});
  const [showUploader, setShowUploader] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch schools
  useEffect(() => {
    const getSchools = async () => {
      try {
        setIsLoading(true);
        const schoolsData = await fetchSchools();
        if (schoolsData.status === 'success' && schoolsData.data) {
          setSchools(schoolsData.data);
          setFilteredSchools(schoolsData.data);
        } else {
          setError('Failed to load schools');
        }
      } catch (err) {
        setError('An error occurred while fetching schools');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    if (sessionStatus === 'authenticated') {
      getSchools();
    }
  }, [sessionStatus, refreshTrigger]);

  // Check format status for each school
  useEffect(() => {
    const checkFormatStatuses = async () => {
      const statuses: Record<string, FormatStatus> = {};
      
      for (const school of schools) {
        try {
          const response = await getExaminationFormatAction(school.id);
          
          if (response.status === 'success' && response.data) {
            statuses[school.id] = { status: 'available' };
          } else {
            statuses[school.id] = { 
              status: 'unavailable',
              error: response.message || 'Format not available'
            };
          }
        } catch (err) {
          statuses[school.id] = { 
            status: 'error',
            error: err instanceof Error ? err.message : 'Unknown error'
          };
        }
      }
      
      setFormatStatuses(statuses);
    };

    if (schools.length > 0) {
      checkFormatStatuses();
    }
  }, [schools]);

  // Filter schools based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredSchools(schools);
      return;
    }
    
    const query = searchQuery.toLowerCase();
    const filtered = schools.filter(school => 
      school.name.toLowerCase().includes(query) ||
      school.email.toLowerCase().includes(query) ||
      school.address.toLowerCase().includes(query)
    );
    
    setFilteredSchools(filtered);
  }, [searchQuery, schools]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleViewFormat = (schoolId: string) => {
    router.push(`/school-management/${schoolId}?tab=format`);
  };

  const handleUploadSuccess = () => {
    setShowUploader(null);
    setRefreshTrigger(prev => prev + 1);
  };

  const getStatusIcon = (status: FormatStatus['status']) => {
    switch (status) {
      case 'available':
        return <FiCheck className="text-green-500" size={20} />;
      case 'unavailable':
        return <FiX className="text-red-500" size={20} />;
      case 'processing':
        return <FiClock className="text-yellow-500" size={20} />;
      case 'error':
        return <FiX className="text-red-500" size={20} />;
      default:
        return null;
    }
  };

  const getStatusText = (status: FormatStatus['status']) => {
    switch (status) {
      case 'available':
        return 'Available';
      case 'unavailable':
        return 'Not Available';
      case 'processing':
        return 'Processing';
      case 'error':
        return 'Error';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status: FormatStatus['status']) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'unavailable':
        return 'bg-red-100 text-red-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Check if user is authenticated and has admin role
  if (sessionStatus === 'loading') {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="lg" />
      </div>
    );
  }

  if (sessionStatus === 'unauthenticated') {
    router.push('/auth/sign-in');
    return null;
  }

  if (session?.user?.role !== EUserRole.ADMIN) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="error">
          You do not have permission to access this page.
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Examination Formats</h1>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={isLoading}
        >
          <FiRefreshCw className="mr-2" />
          Refresh
        </Button>
      </div>

      {error && (
        <Alert variant="error" className="mb-6" onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Search and Filter */}
      <div className="mb-6">
        <div className="relative">
          <Input
            type="text"
            placeholder="Search schools..."
            value={searchQuery}
            onChange={handleSearch}
            className="pl-10"
          />
          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        </div>
      </div>

      {/* Schools Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        {isLoading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="lg" />
            <span className="ml-3 text-gray-600">Loading schools...</span>
          </div>
        ) : filteredSchools.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            {searchQuery ? 'No schools match your search' : 'No schools found'}
          </div>
        ) : (
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  School
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Format Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredSchools.map((school) => {
                const formatStatus = formatStatuses[school.id] || { status: 'unavailable' };
                return (
                  <React.Fragment key={school.id}>
                    <tr className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{school.name}</div>
                        <div className="text-sm text-gray-500">{school.address}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{school.email}</div>
                        <div className="text-sm text-gray-500">{school.phoneNumber}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(formatStatus.status)}
                          <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(formatStatus.status)}`}>
                            {getStatusText(formatStatus.status)}
                          </span>
                        </div>
                        {formatStatus.error && (
                          <div className="text-xs text-red-500 mt-1">{formatStatus.error}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          {formatStatus.status === 'available' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewFormat(school.id)}
                            >
                              <FiEye className="mr-1" /> View
                            </Button>
                          )}
                          <Button
                            variant={formatStatus.status === 'available' ? 'outline' : 'primary'}
                            size="sm"
                            onClick={() => setShowUploader(school.id)}
                          >
                            <FiUpload className="mr-1" /> {formatStatus.status === 'available' ? 'Update' : 'Upload'}
                          </Button>
                        </div>
                      </td>
                    </tr>
                    {showUploader === school.id && (
                      <tr>
                        <td colSpan={4} className="px-6 py-4 bg-gray-50">
                          <ExaminationFormatUploader
                            schoolId={school.id}
                            onUploadSuccess={handleUploadSuccess}
                            onUploadError={() => {}}
                          />
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                );
              })}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
};

export default ExaminationFormatsPage;