'use client';

import * as React from 'react';
import Link from 'next/link';
import { cn } from '@/utils/cn';
import Icon, { IconProps } from '@/components/atoms/Icon/Icon';
import { cva, type VariantProps } from 'class-variance-authority';

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  isLoading?: boolean; // Optional prop to show a loading state
  href?: string; // Optional href for redirection
  iconProps?: IconProps; // Props for rendering an optional icon
}

// Define button variants using `cva`
const buttonVariants = cva('btn p-3 w-[7.5rem]', {
  variants: {
    variant: {
      primary: 'btn-primary',
      secondary: 'btn-secondary',
      accent: 'btn-accent',
      ghost: 'btn-ghost',
      outline: 'btn-outline border-gray-200 text-gray-300',
      error: 'btn-error',
    },
  },
  defaultVariants: {
    variant: 'primary',
  },
});

const Button: React.FC<ButtonProps> = ({
  children,
  className,
  isLoading = false,
  href,
  iconProps,
  variant,
  ...props
}) => {
  const content = (
    <div className="inline-flex items-center justify-center">
      {isLoading && (
        <span className="absolute left-4 inline-flex h-4 w-4 animate-spin rounded-full border-2 border-t-transparent border-white"></span>
      )}
      {iconProps && (
        <Icon
          {...iconProps}
          className={cn('mr-2', iconProps.className)} // Add spacing for the icon
        />
      )}
      <span
        className={cn(
          isLoading ? 'opacity-0' : '',
          'text-sm font-medium transition-all'
        )}
      >
        {children}
      </span>
    </div>
  );

  if (href) {
    // Use Next.js Link for redirection if href is provided
    return (
      <Link
        href={href}
        className={cn(
          buttonVariants({ variant }),
          className, // Ensure custom className is applied after buttonVariants
          isLoading && 'opacity-75 cursor-not-allowed'
        )}
      >
        {content}
      </Link>
    );
  }

  // Render a native button if no href is provided
  return (
    <button
      className={cn(
        buttonVariants({ variant }),
        className, // Ensure custom className is applied after buttonVariants
        isLoading && 'opacity-75 cursor-not-allowed'
      )}
      {...props}
    >
      {content}
    </button>
  );
};

export { Button, buttonVariants };
