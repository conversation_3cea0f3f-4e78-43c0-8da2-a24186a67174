'use client';

import React, { useState } from 'react';
import { ExaminationFormatUploader } from './ExaminationFormatUploader';
import { ExaminationFormatViewer } from './ExaminationFormatViewer';
import { Button } from '@/components/atoms/Button';
import { deleteExaminationFormatAction } from '@/actions/examinationFormat.action';
import { FiTrash2, FiAlertTriangle } from 'react-icons/fi';
import { Alert } from '@/components/atoms/Alert';

interface ExaminationFormatManagerProps {
  schoolId: string;
  schoolName?: string;
  className?: string;
  metadata?: {
    uploadedAt?: string;
    fileSize?: number;
    uploader?: string;
  };
}

export const ExaminationFormatManager: React.FC<ExaminationFormatManagerProps> = ({
  schoolId,
  schoolName,
  className,
  metadata,
}) => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleUploadSuccess = () => {
    setRefreshTrigger(prev => prev + 1);
    setSuccess('Format uploaded successfully');
    setError(null);
  };

  const handleUploadError = (errorMessage: string) => {
    setError(errorMessage);
    setSuccess(null);
  };

  const handleDeleteFormat = async () => {
    if (!schoolId) return;
    
    setIsDeleting(true);
    setError(null);
    setSuccess(null);
    
    try {
      const response = await deleteExaminationFormatAction(schoolId);
      
      if (response.status === 'success') {
        setSuccess('Examination format deleted successfully');
        setRefreshTrigger(prev => prev + 1);
      } else {
        setError(response.message || 'Failed to delete examination format');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Messages */}
      {error && (
        <Alert variant="error" onClose={() => setError(null)}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert variant="success" onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}
      
      {/* Format Viewer */}
      <ExaminationFormatViewer
        schoolId={schoolId}
        schoolName={schoolName}
        refreshTrigger={refreshTrigger}
        metadata={metadata}
      />
      
      {/* Delete Confirmation */}
      {showDeleteConfirm && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mt-4">
          <div className="flex items-start">
            <FiAlertTriangle className="text-red-500 mt-0.5 mr-3" size={20} />
            <div>
              <h4 className="font-medium text-red-800">Confirm Deletion</h4>
              <p className="text-sm text-red-700 mt-1">
                Are you sure you want to delete this examination format? This action cannot be undone.
              </p>
              <div className="mt-3 flex space-x-3">
                <Button
                  variant="danger"
                  size="sm"
                  onClick={handleDeleteFormat}
                  disabled={isDeleting}
                >
                  {isDeleting ? 'Deleting...' : 'Yes, Delete Format'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowDeleteConfirm(false)}
                  disabled={isDeleting}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Action Buttons */}
      <div className="flex justify-between items-center">
        <Button
          variant="danger"
          onClick={() => setShowDeleteConfirm(true)}
          disabled={isDeleting || showDeleteConfirm}
        >
          <FiTrash2 className="mr-2" />
          Delete Format
        </Button>
        
        <div className="flex-1 ml-4">
          <ExaminationFormatUploader
            schoolId={schoolId}
            onUploadSuccess={handleUploadSuccess}
            onUploadError={handleUploadError}
          />
        </div>
      </div>
    </div>
  );
};