'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Button } from '@/components/atoms/Button';
import { Spinner } from '@/components/atoms/Spinner';
import { Alert } from '@/components/atoms/Alert';
import { SchoolSelector } from '@/components/molecules/SchoolSelector';
import { uploadExaminationFormatAction } from '@/actions/examinationFormat.action';
import { fetchSchools } from '@/actions/school.action';
import { useForm, Controller } from 'react-hook-form';
import { FiUpload, FiFile, FiX } from 'react-icons/fi';

interface ExaminationFormatUploaderProps {
  schoolId?: string;
  onUploadSuccess?: (response: any) => void;
  onUploadError?: (error: string) => void;
  className?: string;
}

interface UploadFormData {
  schoolId: string;
}

export const ExaminationFormatUploader: React.FC<ExaminationFormatUploaderProps> = ({
  schoolId: initialSchoolId,
  onUploadSuccess,
  onUploadError,
  className,
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { control, handleSubmit, formState: { errors }, watch, setValue } = useForm<UploadFormData>({
    defaultValues: {
      schoolId: initialSchoolId || '',
    },
  });

  const watchedSchoolId = watch('schoolId');

  // Reset file when school changes
  React.useEffect(() => {
    if (initialSchoolId && initialSchoolId !== watchedSchoolId) {
      setValue('schoolId', initialSchoolId);
    }
  }, [initialSchoolId, setValue, watchedSchoolId]);

  const validateFile = (file: File): string | null => {
    // Check file type
    if (file.type !== 'application/pdf') {
      return 'Only PDF files are allowed';
    }
    
    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      return 'File size exceeds 5MB limit';
    }
    
    return null;
  };

  const handleFileChange = (selectedFile: File | null) => {
    setError(null);
    setSuccess(null);
    
    if (!selectedFile) {
      setFile(null);
      return;
    }
    
    const validationError = validateFile(selectedFile);
    if (validationError) {
      setError(validationError);
      setFile(null);
      return;
    }
    
    setFile(selectedFile);
  };

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileChange(e.dataTransfer.files[0]);
    }
  }, []);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFileChange(e.target.files[0]);
    }
  };

  const handleRemoveFile = () => {
    setFile(null);
    setError(null);
    setSuccess(null);
    setUploadProgress(0);
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const simulateProgress = () => {
    setUploadProgress(0);
    const interval = setInterval(() => {
      setUploadProgress((prevProgress) => {
        const newProgress = prevProgress + Math.random() * 10;
        return newProgress >= 90 ? 90 : newProgress; // Cap at 90% until actual completion
      });
    }, 300);
    return interval;
  };

  const onSubmit = async (data: UploadFormData) => {
    if (!file) {
      setError('Please select a file to upload');
      return;
    }

    if (!data.schoolId) {
      setError('Please select a school');
      return;
    }

    setError(null);
    setSuccess(null);
    setIsUploading(true);
    
    // Start progress simulation
    const progressInterval = simulateProgress();

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('schoolId', data.schoolId);

      const response = await uploadExaminationFormatAction(formData);
      
      // Clear progress interval
      clearInterval(progressInterval);
      
      if (response.status === 'success') {
        setUploadProgress(100);
        setSuccess('Examination format uploaded successfully');
        setFile(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        if (onUploadSuccess) {
          onUploadSuccess(response.data);
        }
      } else {
        setError(response.message || 'Upload failed');
        if (onUploadError) {
          onUploadError(response.message || 'Upload failed');
        }
      }
    } catch (err) {
      clearInterval(progressInterval);
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      if (onUploadError) {
        onUploadError(errorMessage);
      }
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className || ''}`}>
      <h3 className="text-lg font-semibold mb-4">Upload Examination Format</h3>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {/* School Selector */}
        {!initialSchoolId && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Select School
            </label>
            <Controller
              name="schoolId"
              control={control}
              rules={{ required: 'School is required' }}
              render={({ field }) => (
                <SchoolSelector
                  {...field}
                  error={errors.schoolId?.message}
                  fetchSchools={fetchSchools}
                  control={control}
                />
              )}
            />
            {errors.schoolId && (
              <p className="mt-1 text-sm text-red-600">{errors.schoolId.message}</p>
            )}
          </div>
        )}
        
        {/* File Upload Area */}
        <div className="mt-4">
          {file ? (
            // File Preview
            <div className="border rounded-lg p-4 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FiFile className="text-blue-500 mr-2" size={24} />
                  <div>
                    <p className="font-medium">{file.name}</p>
                    <p className="text-sm text-gray-500">
                      {(file.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={handleRemoveFile}
                  className="text-gray-500 hover:text-red-500"
                  disabled={isUploading}
                >
                  <FiX size={20} />
                </button>
              </div>
              
              {isUploading && (
                <div className="mt-3">
                  <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-500 transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1 text-right">
                    {Math.round(uploadProgress)}%
                  </p>
                </div>
              )}
            </div>
          ) : (
            // Drag & Drop Zone
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}`}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <FiUpload className="mx-auto text-gray-400 mb-2" size={32} />
              <p className="text-gray-700 font-medium">Drag & drop your PDF file here</p>
              <p className="text-sm text-gray-500 mt-1">or click to browse</p>
              <p className="text-xs text-gray-400 mt-2">Maximum file size: 5MB</p>
            </div>
          )}
          
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileInputChange}
            accept="application/pdf"
            className="hidden"
            disabled={isUploading}
          />
        </div>
        
        {/* Error and Success Messages */}
        {error && (
          <Alert variant="error" className="mt-4">
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert variant="success" className="mt-4">
            {success}
          </Alert>
        )}
        
        {/* Submit Button */}
        <div className="mt-6">
          <Button
            type="submit"
            variant="primary"
            className="w-full"
            disabled={isUploading || !file}
          >
            {isUploading ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Uploading...
              </>
            ) : (
              'Upload Format'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};