'use client';

import React, { useState, useEffect } from 'react';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { Button } from '@/components/atoms/Button/Button';
import { getExaminationFormatAction } from '@/actions/examinationFormat.action';
import { Download, ExternalLink, Calendar, FileText, User, Loader2 } from 'lucide-react';

interface ExaminationFormatViewerProps {
  schoolId: string;
  schoolName?: string;
  refreshTrigger?: number; // Increment this to trigger a refresh
  className?: string;
  metadata?: {
    uploadedAt?: string;
    fileSize?: number;
    uploader?: string;
  };
}

export const ExaminationFormatViewer: React.FC<ExaminationFormatViewerProps> = ({
  schoolId,
  schoolName,
  refreshTrigger = 0,
  className,
  metadata,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formatData, setFormatData] = useState<{
    text?: string;
    url?: string;
    contentType?: string;
  } | null>(null);
  const [isPdfLoading, setIsPdfLoading] = useState(true);

  const fetchFormat = async () => {
    if (!schoolId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await getExaminationFormatAction(schoolId);
      
      if (response.status === 'success') {
        setFormatData(response.data);
      } else {
        setError(typeof response.message === 'string' ? response.message : 'Failed to load examination format');
        setFormatData(null);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      setFormatData(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchFormat();
  }, [schoolId, refreshTrigger]);

  const handlePdfLoad = () => {
    setIsPdfLoading(false);
  };

  const handlePdfError = () => {
    setIsPdfLoading(false);
    setError('Failed to load PDF preview');
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown';
    if (bytes < 1024) return `${bytes} bytes`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
  };

  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className || ''}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">
          {schoolName ? `${schoolName} Format` : 'Examination Format'}
        </h3>
        
        {formatData?.url && (
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => window.open(formatData.url, '_blank')}
            >
              <ExternalLink className="mr-1" /> Open
            </Button>
            <a
              href={formatData.url}
              download="examination_format.pdf"
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <Download className="mr-1" /> Download
            </a>
          </div>
        )}
      </div>

      {/* Metadata Section */}
      {(formatData || metadata) && (
        <div className="bg-gray-50 rounded p-3 mb-4 text-sm">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            {metadata?.uploadedAt && (
              <div className="flex items-center">
                <Calendar className="text-gray-500 mr-2" />
                <div>
                  <span className="text-gray-600 font-medium">Uploaded:</span>{' '}
                  <span>{formatDate(metadata.uploadedAt)}</span>
                </div>
              </div>
            )}
            {metadata?.fileSize && (
              <div className="flex items-center">
                <FileText className="text-gray-500 mr-2" />
                <div>
                  <span className="text-gray-600 font-medium">Size:</span>{' '}
                  <span>{formatFileSize(metadata.fileSize)}</span>
                </div>
              </div>
            )}
            {metadata?.uploader && (
              <div className="flex items-center">
                <User className="text-gray-500 mr-2" />
                <div>
                  <span className="text-gray-600 font-medium">By:</span>{' '}
                  <span>{metadata.uploader}</span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Content Display */}
      <div className="border rounded-lg overflow-hidden bg-gray-50 min-h-[400px] flex flex-col">
        {isLoading ? (
          <div className="flex-1 flex items-center justify-center p-8">
            <Loader2 size={32} className="animate-spin text-blue-600" />
            <span className="ml-3 text-gray-600">Loading format...</span>
          </div>
        ) : error ? (
          <div className="flex-1 flex items-center justify-center p-8">
            <AlertMessage type="error" message={error} />
          </div>
        ) : !formatData ? (
          <div className="flex-1 flex items-center justify-center p-8 text-center">
            <div>
              <p className="text-gray-500 mb-2">No examination format available</p>
              <p className="text-sm text-gray-400">Upload a format to see it here</p>
            </div>
          </div>
        ) : formatData.url ? (
          <div className="relative flex-1 min-h-[400px]">
            {isPdfLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-80 z-10">
                <Loader2 size={32} className="animate-spin text-blue-600" />
                <span className="ml-3 text-gray-600">Loading PDF preview...</span>
              </div>
            )}
            <iframe
              src={formatData.url}
              className="w-full h-full min-h-[400px] border-0"
              onLoad={handlePdfLoad}
              onError={handlePdfError}
              title="Examination Format PDF"
            />
          </div>
        ) : formatData.text ? (
          <div className="flex-1 p-4">
            <pre className="whitespace-pre-wrap font-mono text-sm">{formatData.text}</pre>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center p-8 text-center">
            <div>
              <p className="text-gray-500">Format content is empty or in an unsupported format</p>
            </div>
          </div>
        )}
      </div>

      {/* Refresh Button */}
      <div className="mt-4 text-right">
        <Button
          variant="outline"
          size="sm"
          onClick={fetchFormat}
          disabled={isLoading}
        >
          {isLoading ? <Spinner size="sm" className="mr-2" /> : null}
          Refresh
        </Button>
      </div>
    </div>
  );
};